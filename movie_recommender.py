#!/usr/bin/env python3
"""
Movie Recommendation System
A CLI-based content filtering movie recommendation system using TMDB dataset.
"""

import pandas as pd
import numpy as np
import json
import ast
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import StandardScaler
import argparse
import sys
import warnings
warnings.filterwarnings('ignore')

class MovieRecommendationSystem:
    def __init__(self, movies_file='tmdb_5000_movies.csv', credits_file='tmdb_5000_credits.csv'):
        """Initialize the recommendation system with data files."""
        self.movies_file = movies_file
        self.credits_file = credits_file
        self.movies_df = None
        self.credits_df = None
        self.combined_df = None
        self.tfidf_matrix = None
        self.cosine_sim = None
        self.scaler = StandardScaler()
        
    def load_data(self):
        """Load and merge the movie and credits datasets."""
        print("Loading movie datasets...")
        try:
            self.movies_df = pd.read_csv(self.movies_file)
            self.credits_df = pd.read_csv(self.credits_file)
            print(f"Loaded {len(self.movies_df)} movies and {len(self.credits_df)} credit records")
        except FileNotFoundError as e:
            print(f"Error: Could not find data files. {e}")
            sys.exit(1)
        except Exception as e:
            print(f"Error loading data: {e}")
            sys.exit(1)
    
    def safe_literal_eval(self, val):
        """Safely evaluate string representations of Python literals."""
        try:
            if pd.isna(val) or val == '':
                return []
            return ast.literal_eval(val)
        except (ValueError, SyntaxError):
            return []
    
    def extract_names(self, obj_list, key='name', limit=3):
        """Extract names from list of dictionaries."""
        if not obj_list:
            return []
        return [obj[key] for obj in obj_list[:limit] if key in obj]
    
    def extract_cast_names(self, cast_list, limit=5):
        """Extract actor names from cast list."""
        if not cast_list:
            return []
        return [actor['name'] for actor in cast_list[:limit] if 'name' in actor]
    
    def extract_director(self, crew_list):
        """Extract director name from crew list."""
        if not crew_list:
            return ''
        for person in crew_list:
            if person.get('job') == 'Director':
                return person.get('name', '')
        return ''
    
    def preprocess_data(self):
        """Clean and preprocess the movie data."""
        print("Preprocessing data...")
        
        # Merge datasets
        self.combined_df = self.movies_df.merge(self.credits_df, left_on='id', right_on='movie_id', how='left')

        # Fix column names after merge (title becomes title_x and title_y)
        if 'title_x' in self.combined_df.columns:
            self.combined_df['title'] = self.combined_df['title_x']
            self.combined_df = self.combined_df.drop(['title_x', 'title_y'], axis=1)
        
        # Handle missing values
        self.combined_df['overview'] = self.combined_df['overview'].fillna('')
        self.combined_df['tagline'] = self.combined_df['tagline'].fillna('')
        self.combined_df['genres'] = self.combined_df['genres'].fillna('[]')
        self.combined_df['keywords'] = self.combined_df['keywords'].fillna('[]')
        self.combined_df['cast'] = self.combined_df['cast'].fillna('[]')
        self.combined_df['crew'] = self.combined_df['crew'].fillna('[]')
        
        # Parse JSON-like strings
        self.combined_df['genres_parsed'] = self.combined_df['genres'].apply(self.safe_literal_eval)
        self.combined_df['keywords_parsed'] = self.combined_df['keywords'].apply(self.safe_literal_eval)
        self.combined_df['cast_parsed'] = self.combined_df['cast'].apply(self.safe_literal_eval)
        self.combined_df['crew_parsed'] = self.combined_df['crew'].apply(self.safe_literal_eval)
        
        # Extract features
        self.combined_df['genre_names'] = self.combined_df['genres_parsed'].apply(lambda x: self.extract_names(x))
        self.combined_df['keyword_names'] = self.combined_df['keywords_parsed'].apply(lambda x: self.extract_names(x, limit=5))
        self.combined_df['cast_names'] = self.combined_df['cast_parsed'].apply(lambda x: self.extract_cast_names(x))
        self.combined_df['director'] = self.combined_df['crew_parsed'].apply(self.extract_director)
        
        # Create combined features for content-based filtering
        self.combined_df['combined_features'] = (
            self.combined_df['overview'] + ' ' +
            self.combined_df['tagline'] + ' ' +
            self.combined_df['genre_names'].apply(lambda x: ' '.join(x)) + ' ' +
            self.combined_df['keyword_names'].apply(lambda x: ' '.join(x)) + ' ' +
            self.combined_df['cast_names'].apply(lambda x: ' '.join(x)) + ' ' +
            self.combined_df['director']
        )
        
        # Clean up the combined features
        self.combined_df['combined_features'] = self.combined_df['combined_features'].str.lower()
        
        # Handle numerical features
        numerical_features = ['vote_average', 'popularity', 'runtime']
        for feature in numerical_features:
            self.combined_df[feature] = pd.to_numeric(self.combined_df[feature], errors='coerce')
            self.combined_df[feature] = self.combined_df[feature].fillna(self.combined_df[feature].median())
        
        print(f"Preprocessed {len(self.combined_df)} movies")
    
    def build_recommendation_model(self):
        """Build the TF-IDF matrix and compute cosine similarity."""
        print("Building recommendation model...")
        
        # Create TF-IDF matrix
        tfidf = TfidfVectorizer(
            max_features=10000,
            stop_words='english',
            ngram_range=(1, 2),
            min_df=2,
            max_df=0.8
        )
        
        self.tfidf_matrix = tfidf.fit_transform(self.combined_df['combined_features'])
        
        # Compute cosine similarity matrix
        self.cosine_sim = cosine_similarity(self.tfidf_matrix, self.tfidf_matrix)
        
        print("Model built successfully!")
    
    def get_movie_recommendations(self, movie_title, num_recommendations=10):
        """Get movie recommendations based on a given movie title."""
        # Find the movie index
        movie_indices = self.combined_df[self.combined_df['title'].str.contains(movie_title, case=False, na=False)].index
        
        if len(movie_indices) == 0:
            return None, f"Movie '{movie_title}' not found in the database."
        
        # Use the first match
        movie_idx = movie_indices[0]
        movie_info = self.combined_df.iloc[movie_idx]
        
        # Get similarity scores
        sim_scores = list(enumerate(self.cosine_sim[movie_idx]))
        sim_scores = sorted(sim_scores, key=lambda x: x[1], reverse=True)
        
        # Get top recommendations (excluding the movie itself)
        movie_indices = [i[0] for i in sim_scores[1:num_recommendations+1]]
        
        recommendations = self.combined_df.iloc[movie_indices][
            ['title', 'vote_average', 'popularity', 'release_date', 'genre_names', 'overview']
        ].copy()
        
        return movie_info, recommendations
    
    def get_recommendations_by_preferences(self, genres=None, min_rating=0, min_year=1900, max_year=2030, num_recommendations=10):
        """Get recommendations based on user preferences."""
        filtered_df = self.combined_df.copy()
        
        # Filter by genres
        if genres:
            genre_filter = filtered_df['genre_names'].apply(
                lambda x: any(genre.lower() in [g.lower() for g in x] for genre in genres)
            )
            filtered_df = filtered_df[genre_filter]
        
        # Filter by rating
        filtered_df = filtered_df[filtered_df['vote_average'] >= min_rating]
        
        # Filter by year
        filtered_df['release_year'] = pd.to_datetime(filtered_df['release_date'], errors='coerce').dt.year
        filtered_df = filtered_df[
            (filtered_df['release_year'] >= min_year) & 
            (filtered_df['release_year'] <= max_year)
        ]
        
        if len(filtered_df) == 0:
            return None, "No movies found matching your criteria."
        
        # Sort by a combination of rating and popularity
        filtered_df['score'] = (filtered_df['vote_average'] * 0.7 + 
                               np.log1p(filtered_df['popularity']) * 0.3)
        
        recommendations = filtered_df.nlargest(num_recommendations, 'score')[
            ['title', 'vote_average', 'popularity', 'release_date', 'genre_names', 'overview']
        ]
        
        return None, recommendations
    
    def display_recommendations(self, movie_info, recommendations, title="Recommendations"):
        """Display recommendations in a formatted way."""
        print(f"\n{'='*60}")
        print(f"{title.upper()}")
        print(f"{'='*60}")
        
        if movie_info is not None:
            print(f"\nBased on: {movie_info['title']} ({movie_info.get('release_date', 'N/A')[:4]})")
            print(f"Rating: {movie_info['vote_average']}/10")
            print(f"Genres: {', '.join(movie_info['genre_names'])}")
            print(f"Overview: {movie_info['overview'][:200]}...")
            print(f"\n{'-'*60}")
        
        if isinstance(recommendations, str):
            print(f"\n{recommendations}")
            return
        
        for idx, (_, movie) in enumerate(recommendations.iterrows(), 1):
            print(f"\n{idx}. {movie['title']}")
            print(f"   Rating: {movie['vote_average']}/10")
            print(f"   Release: {movie.get('release_date', 'N/A')[:4]}")
            print(f"   Genres: {', '.join(movie['genre_names']) if movie['genre_names'] else 'N/A'}")
            print(f"   Overview: {movie['overview'][:150]}..." if len(movie['overview']) > 150 else f"   Overview: {movie['overview']}")
        
        print(f"\n{'='*60}")

def main():
    """Main CLI interface for the movie recommendation system."""
    parser = argparse.ArgumentParser(description='Movie Recommendation System')
    parser.add_argument('--mode', choices=['similar', 'preferences'], default='similar',
                       help='Recommendation mode: similar (based on a movie) or preferences (based on criteria)')
    parser.add_argument('--movie', type=str, help='Movie title for similarity-based recommendations')
    parser.add_argument('--genres', nargs='+', help='Preferred genres (e.g., Action Comedy Drama)')
    parser.add_argument('--min-rating', type=float, default=0, help='Minimum rating (0-10)')
    parser.add_argument('--min-year', type=int, default=1900, help='Minimum release year')
    parser.add_argument('--max-year', type=int, default=2030, help='Maximum release year')
    parser.add_argument('--count', type=int, default=10, help='Number of recommendations')
    parser.add_argument('--interactive', action='store_true', help='Run in interactive mode')

    args = parser.parse_args()

    # Initialize the recommendation system
    recommender = MovieRecommendationSystem()

    try:
        # Load and preprocess data
        recommender.load_data()
        recommender.preprocess_data()
        recommender.build_recommendation_model()

        if args.interactive:
            interactive_mode(recommender)
        else:
            if args.mode == 'similar':
                if not args.movie:
                    print("Error: --movie is required for similarity-based recommendations")
                    sys.exit(1)

                movie_info, recommendations = recommender.get_movie_recommendations(
                    args.movie, args.count
                )

                if movie_info is None:
                    print(recommendations)  # Error message
                else:
                    recommender.display_recommendations(movie_info, recommendations,
                                                      "Movies Similar To Your Choice")

            elif args.mode == 'preferences':
                movie_info, recommendations = recommender.get_recommendations_by_preferences(
                    genres=args.genres,
                    min_rating=args.min_rating,
                    min_year=args.min_year,
                    max_year=args.max_year,
                    num_recommendations=args.count
                )

                if movie_info is None and isinstance(recommendations, str):
                    print(recommendations)  # Error message
                else:
                    recommender.display_recommendations(None, recommendations,
                                                      "Movies Based On Your Preferences")

    except KeyboardInterrupt:
        print("\nGoodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"An error occurred: {e}")
        sys.exit(1)

def interactive_mode(recommender):
    """Interactive CLI mode for the recommendation system."""
    print("\n" + "="*60)
    print("WELCOME TO THE MOVIE RECOMMENDATION SYSTEM")
    print("="*60)
    print("\nChoose your recommendation method:")
    print("1. Find movies similar to a movie you like")
    print("2. Get recommendations based on your preferences")
    print("3. Exit")

    while True:
        try:
            choice = input("\nEnter your choice (1-3): ").strip()

            if choice == '1':
                movie_title = input("Enter a movie title: ").strip()
                if not movie_title:
                    print("Please enter a valid movie title.")
                    continue

                count = input("How many recommendations? (default: 10): ").strip()
                count = int(count) if count.isdigit() else 10

                movie_info, recommendations = recommender.get_movie_recommendations(movie_title, count)

                if movie_info is None:
                    print(f"\n{recommendations}")

                    # Suggest similar titles
                    similar_titles = recommender.combined_df[
                        recommender.combined_df['title'].str.contains(movie_title.split()[0], case=False, na=False)
                    ]['title'].head(5).tolist()

                    if similar_titles:
                        print(f"\nDid you mean one of these?")
                        for i, title in enumerate(similar_titles, 1):
                            print(f"{i}. {title}")
                else:
                    recommender.display_recommendations(movie_info, recommendations,
                                                      "Movies Similar To Your Choice")

            elif choice == '2':
                print("\nLet's find movies based on your preferences!")

                # Get genres
                available_genres = set()
                for genre_list in recommender.combined_df['genre_names']:
                    available_genres.update(genre_list)
                available_genres = sorted(list(available_genres))

                print(f"\nAvailable genres: {', '.join(available_genres)}")
                genres_input = input("Enter preferred genres (comma-separated, or press Enter to skip): ").strip()
                genres = [g.strip() for g in genres_input.split(',')] if genres_input else None

                # Get rating
                min_rating = input("Minimum rating (0-10, default: 0): ").strip()
                min_rating = float(min_rating) if min_rating else 0

                # Get year range
                min_year = input("Minimum release year (default: 1900): ").strip()
                min_year = int(min_year) if min_year.isdigit() else 1900

                max_year = input("Maximum release year (default: 2030): ").strip()
                max_year = int(max_year) if max_year.isdigit() else 2030

                # Get count
                count = input("How many recommendations? (default: 10): ").strip()
                count = int(count) if count.isdigit() else 10

                movie_info, recommendations = recommender.get_recommendations_by_preferences(
                    genres=genres, min_rating=min_rating, min_year=min_year,
                    max_year=max_year, num_recommendations=count
                )

                if movie_info is None and isinstance(recommendations, str):
                    print(f"\n{recommendations}")
                else:
                    recommender.display_recommendations(None, recommendations,
                                                      "Movies Based On Your Preferences")

            elif choice == '3':
                print("Thank you for using the Movie Recommendation System!")
                break

            else:
                print("Invalid choice. Please enter 1, 2, or 3.")

        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except ValueError as e:
            print(f"Invalid input: {e}")
        except Exception as e:
            print(f"An error occurred: {e}")

if __name__ == "__main__":
    main()
