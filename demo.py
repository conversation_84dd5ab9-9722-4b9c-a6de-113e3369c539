#!/usr/bin/env python3
"""
Demo script for the Movie Recommendation System
Shows various examples of how to use the system programmatically.
"""

from movie_recommender import MovieRecommendationSystem

def main():
    print("="*60)
    print("MOVIE RECOMMENDATION SYSTEM DEMO")
    print("="*60)
    
    # Initialize the system
    print("\n1. Initializing the recommendation system...")
    recommender = MovieRecommendationSystem()
    
    # Load and preprocess data
    print("\n2. Loading and preprocessing data...")
    recommender.load_data()
    recommender.preprocess_data()
    recommender.build_recommendation_model()
    
    print("\n" + "="*60)
    print("DEMO 1: SIMILAR MOVIE RECOMMENDATIONS")
    print("="*60)
    
    # Demo 1: Similar movies to "The Dark Knight"
    movie_info, recommendations = recommender.get_movie_recommendations("The Dark Knight", 5)
    if movie_info is not None:
        recommender.display_recommendations(movie_info, recommendations, "Movies Similar to The Dark Knight")
    
    print("\n" + "="*60)
    print("DEMO 2: PREFERENCE-BASED RECOMMENDATIONS")
    print("="*60)
    
    # Demo 2: Action/Adventure movies with high ratings
    movie_info, recommendations = recommender.get_recommendations_by_preferences(
        genres=['Action', 'Adventure'],
        min_rating=7.5,
        min_year=2000,
        num_recommendations=5
    )
    recommender.display_recommendations(None, recommendations, "High-Rated Action/Adventure Movies (2000+)")
    
    print("\n" + "="*60)
    print("DEMO 3: COMEDY RECOMMENDATIONS")
    print("="*60)
    
    # Demo 3: Comedy movies
    movie_info, recommendations = recommender.get_recommendations_by_preferences(
        genres=['Comedy'],
        min_rating=6.5,
        min_year=1990,
        max_year=2015,
        num_recommendations=5
    )
    recommender.display_recommendations(None, recommendations, "Comedy Movies (1990-2015)")
    
    print("\n" + "="*60)
    print("DEMO 4: SIMILAR TO POPULAR MOVIE")
    print("="*60)
    
    # Demo 4: Similar to "Inception"
    movie_info, recommendations = recommender.get_movie_recommendations("Inception", 5)
    if movie_info is not None:
        recommender.display_recommendations(movie_info, recommendations, "Movies Similar to Inception")
    
    print("\n" + "="*60)
    print("DEMO COMPLETED!")
    print("="*60)
    print("\nTo use the system interactively, run:")
    print("python movie_recommender.py --interactive")
    print("\nFor command-line usage, see README.md for examples.")

if __name__ == "__main__":
    main()
